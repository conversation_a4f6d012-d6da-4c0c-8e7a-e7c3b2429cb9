import React from 'react';

const Products: React.FC = () => {
  const products = [
    {
      title: 'Conveyor Systems',
      description: 'Advanced automated conveyor solutions for efficient material handling and transportation across industrial facilities.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['Automated Control', 'High Efficiency', 'Durable Design', 'Scalable Solutions'],
      category: 'Automation'
    },
    {
      title: 'Sorting Solutions',
      description: 'Intelligent AI-powered sorting systems that optimize workflow and dramatically reduce processing time.',
      image: 'https://images.unsplash.com/photo-1586864387967-d02ef85d93e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['AI-Powered', 'Real-time Processing', 'Scalable', 'High Accuracy'],
      category: 'Intelligence'
    },
    {
      title: 'Quality Control',
      description: 'Precision quality control systems ensuring consistent product standards and automated defect detection.',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['Automated Inspection', 'Data Analytics', 'Error Detection', 'Compliance Ready'],
      category: 'Quality'
    },
    {
      title: 'Packaging Systems',
      description: 'Complete packaging automation solutions designed for various industries with flexible configurations.',
      image: 'https://images.unsplash.com/photo-1586864387967-d02ef85d93e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['Flexible Design', 'High Speed', 'Cost Effective', 'Multi-format'],
      category: 'Packaging'
    },
    {
      title: 'Robotic Solutions',
      description: 'Advanced robotic systems for precision manufacturing, assembly processes, and material handling.',
      image: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['Precision Control', 'Programmable', 'Safety Features', 'Collaborative'],
      category: 'Robotics'
    },
    {
      title: 'Control Systems',
      description: 'Comprehensive control systems for monitoring and managing complex industrial operations in real-time.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
      features: ['Remote Monitoring', 'Real-time Data', 'User Friendly', 'Cloud Integration'],
      category: 'Control'
    }
  ];

  return (
    <section id="products" className="py-20 bg-white">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <span className="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-semibold mb-4">
            Our Products
          </span>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Cutting-Edge Industrial Equipment
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Comprehensive product range designed to meet diverse industrial needs. 
            From automation to quality control, we provide technology that drives operational excellence.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product, index) => (
            <div 
              key={index}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group hover:-translate-y-1"
            >
              <div className="relative overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    {product.category}
                  </span>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {product.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {product.description}
                </p>
                
                <div className="space-y-2 mb-6">
                  {product.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <svg className="w-4 h-4 text-primary-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </div>
                  ))}
                </div>
                
                <div className="flex space-x-3">
                  <button className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors duration-300">
                    Learn More
                  </button>
                  <button className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Products;
