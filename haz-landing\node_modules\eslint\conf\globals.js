/**
 * @fileoverview Globals for ecmaVersion/sourceType
 * <AUTHOR>
 */

"use strict";

//-----------------------------------------------------------------------------
// Globals
//-----------------------------------------------------------------------------

const commonjs = {
	exports: true,
	global: false,
	module: false,
	require: false,
};

const es3 = {
	Array: false,
	Boolean: false,
	constructor: false,
	Date: false,
	decodeURI: false,
	decodeURIComponent: false,
	encodeURI: false,
	encodeURIComponent: false,
	Error: false,
	escape: false,
	eval: false,
	EvalError: false,
	Function: false,
	hasOwnProperty: false,
	Infinity: false,
	isFinite: false,
	isNaN: false,
	isPrototypeOf: false,
	Math: false,
	NaN: false,
	Number: false,
	Object: false,
	parseFloat: false,
	parseInt: false,
	propertyIsEnumerable: false,
	RangeError: false,
	ReferenceError: false,
	RegExp: false,
	String: false,
	SyntaxError: false,
	toLocaleString: false,
	toString: false,
	TypeError: false,
	undefined: false,
	unescape: false,
	URIError: false,
	valueOf: false,
};

const es5 = {
	...es3,
	JSON: false,
};

const es2015 = {
	...es5,
	ArrayBuffer: false,
	DataView: false,
	Float32Array: false,
	Float64Array: false,
	Int16Array: false,
	Int32Array: false,
	Int8Array: false,
	Intl: false,
	Map: false,
	Promise: false,
	Proxy: false,
	Reflect: false,
	Set: false,
	Symbol: false,
	Uint16Array: false,
	Uint32Array: false,
	Uint8Array: false,
	Uint8ClampedArray: false,
	WeakMap: false,
	WeakSet: false,
};

// no new globals in ES2016
const es2016 = {
	...es2015,
};

const es2017 = {
	...es2016,
	Atomics: false,
	SharedArrayBuffer: false,
};

// no new globals in ES2018
const es2018 = {
	...es2017,
};

// no new globals in ES2019
const es2019 = {
	...es2018,
};

const es2020 = {
	...es2019,
	BigInt: false,
	BigInt64Array: false,
	BigUint64Array: false,
	globalThis: false,
};

const es2021 = {
	...es2020,
	AggregateError: false,
	FinalizationRegistry: false,
	WeakRef: false,
};

const es2022 = {
	...es2021,
};

const es2023 = {
	...es2022,
};

const es2024 = {
	...es2023,
};

const es2025 = {
	...es2024,
	Float16Array: false,
	Iterator: false,
};

const es2026 = {
	...es2025,
	AsyncDisposableStack: false,
	DisposableStack: false,
	SuppressedError: false,
};

//-----------------------------------------------------------------------------
// Exports
//-----------------------------------------------------------------------------

module.exports = {
	commonjs,
	es3,
	es5,
	es2015,
	es2016,
	es2017,
	es2018,
	es2019,
	es2020,
	es2021,
	es2022,
	es2023,
	es2024,
	es2025,
	es2026,
};
