import React from 'react';

const Projects: React.FC = () => {
  const projects = [
    {
      title: 'Manufacturing Automation Revolution',
      client: 'TechCorp Industries',
      description: 'Complete automation solution for automotive parts manufacturing, transforming traditional processes with cutting-edge technology.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      results: [
        { metric: '40%', label: 'Efficiency Increase' },
        { metric: '25%', label: 'Cost Reduction' },
        { metric: '99.5%', label: 'System Uptime' }
      ],
      duration: '6 months',
      category: 'Automation'
    },
    {
      title: 'Smart Logistics Optimization',
      client: 'Global Shipping Co.',
      description: 'Advanced sorting and distribution system for high-volume package processing with AI-powered routing optimization.',
      image: 'https://images.unsplash.com/photo-*************-d02ef85d93e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      results: [
        { metric: '60%', label: 'Faster Processing' },
        { metric: '30%', label: 'Space Optimization' },
        { metric: '50%', label: 'Error Reduction' }
      ],
      duration: '4 months',
      category: 'Logistics'
    },
    {
      title: 'AI-Powered Quality Control',
      client: 'Precision Manufacturing',
      description: 'Revolutionary AI-powered quality inspection system for electronic component manufacturing with machine learning capabilities.',
      image: 'https://images.unsplash.com/photo-*************-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      results: [
        { metric: '99.9%', label: 'Inspection Accuracy' },
        { metric: '80%', label: 'Speed Increase' },
        { metric: '35%', label: 'Defect Reduction' }
      ],
      duration: '8 months',
      category: 'Quality Control'
    }
  ];

  return (
    <section id="projects" className="py-20 bg-gray-50">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <span className="inline-block bg-primary-100 text-primary-600 px-4 py-2 rounded-full text-sm font-semibold mb-4">
            Success Stories
          </span>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Transforming Industries Worldwide
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover how we've helped industry leaders transform their operations 
            with innovative solutions and measurable results that drive real business value.
          </p>
        </div>
        
        <div className="space-y-20">
          {projects.map((project, index) => (
            <div 
              key={index}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="relative rounded-2xl overflow-hidden shadow-2xl group">
                  <img 
                    src={project.image} 
                    alt={project.title}
                    className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                  <div className="absolute top-6 left-6">
                    <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {project.category}
                    </span>
                  </div>
                  <div className="absolute bottom-6 right-6">
                    <span className="bg-white/90 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">
                      {project.duration}
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Content */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                <div className="mb-4">
                  <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide">
                    {project.client}
                  </span>
                </div>
                
                <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                  {project.title}
                </h3>
                
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  {project.description}
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                  {project.results.map((result, resultIndex) => (
                    <div key={resultIndex} className="text-center p-6 bg-white rounded-xl shadow-lg">
                      <p className="text-3xl font-bold text-primary-600 mb-2">
                        {result.metric}
                      </p>
                      <p className="text-sm text-gray-600 font-medium">
                        {result.label}
                      </p>
                    </div>
                  ))}
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
                    View Full Case Study
                  </button>
                  <button className="border-2 border-gray-300 text-gray-700 hover:border-primary-600 hover:text-primary-600 px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
                    Contact Client
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-primary-600 rounded-2xl p-8 lg:p-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
              Ready to Transform Your Operations?
            </h3>
            <p className="text-primary-100 mb-8 max-w-2xl mx-auto">
              Join hundreds of satisfied clients who have revolutionized their business with our solutions.
            </p>
            <button className="bg-white text-primary-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
              Start Your Project Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
