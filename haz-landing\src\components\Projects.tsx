import React from "react";

const Projects: React.FC = () => {
  const projects = [
    {
      title: "Manufacturing Automation",
      client: "TechCorp Industries",
      description:
        "Complete automation solution for automotive parts manufacturing, increasing efficiency by 40%.",
      image:
        "https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      results: [
        "40% Efficiency Increase",
        "25% Cost Reduction",
        "99.5% Uptime",
      ],
    },
    {
      title: "Logistics Optimization",
      client: "Global Shipping Co.",
      description:
        "Advanced sorting and distribution system for high-volume package processing.",
      image:
        "https://images.unsplash.com/photo-1586864387967-d02ef85d93e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      results: [
        "60% Faster Processing",
        "30% Space Optimization",
        "50% Error Reduction",
      ],
    },
    {
      title: "Quality Control System",
      client: "Precision Manufacturing",
      description:
        "AI-powered quality inspection system for electronic component manufacturing.",
      image:
        "https://images.unsplash.com/photo-1581092160562-40aa08e78837?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      results: [
        "99.9% Accuracy",
        "80% Inspection Speed",
        "35% Defect Reduction",
      ],
    },
  ];

  return (
    <section id="projects" className="py-20 bg-white">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Projects
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover how we've helped industry leaders transform their
            operations with innovative solutions and measurable results.
          </p>
        </div>

        <div className="space-y-16">
          {projects.map((project, index) => (
            <div
              key={index}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? "lg:grid-flow-col-dense" : ""
              }`}
            >
              {/* Image */}
              <div className={`${index % 2 === 1 ? "lg:col-start-2" : ""}`}>
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-80 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
              </div>

              {/* Content */}
              <div
                className={`${
                  index % 2 === 1 ? "lg:col-start-1 lg:row-start-1" : ""
                }`}
              >
                <div className="mb-4">
                  <span className="text-primary-600 font-semibold text-sm uppercase tracking-wide">
                    {project.client}
                  </span>
                </div>

                <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                  {project.title}
                </h3>

                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  {project.description}
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                  {project.results.map((result, resultIndex) => (
                    <div
                      key={resultIndex}
                      className="text-center p-4 bg-gray-50 rounded-lg"
                    >
                      <p className="text-2xl font-bold text-primary-600 mb-1">
                        {result.split(" ")[0]}
                      </p>
                      <p className="text-sm text-gray-600">
                        {result.split(" ").slice(1).join(" ")}
                      </p>
                    </div>
                  ))}
                </div>

                <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
                  View Case Study
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
