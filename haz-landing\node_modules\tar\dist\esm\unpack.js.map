{"version": 3, "file": "unpack.js", "sourceRoot": "", "sources": ["../../src/unpack.ts"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,wEAAwE;AACxE,yEAAyE;AACzE,wEAAwE;AACxE,8DAA8D;AAE9D,OAAO,KAAK,GAAG,MAAM,qBAAqB,CAAA;AAC1C,OAAO,MAAM,MAAM,aAAa,CAAA;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAA;AACzC,OAAO,EAAkB,MAAM,SAAS,CAAA;AACxC,OAAO,IAAI,MAAM,WAAW,CAAA;AAC5B,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,EAAE,KAAK,EAAc,SAAS,EAAE,MAAM,YAAY,CAAA;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AACzD,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAA;AACnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAA;AAC5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAClE,OAAO,KAAK,EAAE,MAAM,eAAe,CAAA;AAGnC,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AAIzD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AACzC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;AACzB,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;AACzB,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACxC,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,OAAO,CAAC,QAAQ,CAAA;AAC3D,MAAM,SAAS,GAAG,QAAQ,KAAK,OAAO,CAAA;AACtC,MAAM,iBAAiB,GAAG,IAAI,CAAA;AAE9B,qCAAqC;AACrC,EAAE;AACF,gEAAgE;AAChE,oEAAoE;AACpE,mEAAmE;AACnE,qEAAqE;AACrE,oEAAoE;AACpE,2CAA2C;AAC3C,EAAE;AACF,uEAAuE;AACvE,wEAAwE;AACxE,oEAAoE;AACpE,aAAa;AACb,EAAE;AACF,kDAAkD;AAClD,qBAAqB;AACrB,MAAM,UAAU,GAAG,CACjB,IAAY,EACZ,EAA+B,EAC/B,EAAE;IACF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IAC5B,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAChE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE;QACzB,IAAI,EAAE,EAAE,CAAC;YACP,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACf,CAAC;QACD,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACrB,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AACD,oBAAoB;AAEpB,qBAAqB;AACrB,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,EAAE;IACtC,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,GAAG,UAAU,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAChE,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;AACrB,CAAC,CAAA;AACD,oBAAoB;AAEpB,uCAAuC;AACvC,MAAM,MAAM,GAAG,CACb,CAAqB,EACrB,CAAqB,EACrB,CAAqB,EACrB,EAAE,CACF,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAA;AAEL,sEAAsE;AACtE,yEAAyE;AACzE,wEAAwE;AACxE,uEAAuE;AACvE,yEAAyE;AACzE,YAAY;AACZ,mEAAmE;AACnE,qEAAqE;AACrE,yEAAyE;AACzE,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE,CACzC,oBAAoB,CAClB,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAC7C,CAAC,WAAW,EAAE,CAAA;AAEjB,8CAA8C;AAC9C,MAAM,UAAU,GAAG,CAAC,KAA2B,EAAE,GAAW,EAAE,EAAE;IAC9D,GAAG,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAA;IAC5B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAA;QACrC,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,SAAS,GAAG,CAAC,KAA2B,EAAE,EAAE;IAChD,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;QAC/B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IACnB,CAAC;AACH,CAAC,CAAA;AAED,MAAM,OAAO,MAAO,SAAQ,MAAM;IAChC,CAAC,KAAK,CAAC,GAAY,KAAK,CAAC;IACzB,CAAC,WAAW,CAAC,GAAY,KAAK,CAAC;IAC/B,CAAC,OAAO,CAAC,GAAW,CAAC,CAAA;IAErB,YAAY,GAAqB,IAAI,gBAAgB,EAAE,CAAA;IACvD,SAAS,CAA0B;IACnC,QAAQ,GAAS,IAAI,CAAA;IACrB,QAAQ,GAAU,KAAK,CAAA;IACvB,QAAQ,CAA4C;IACpD,GAAG,CAAS;IACZ,GAAG,CAAS;IACZ,QAAQ,CAAS;IACjB,aAAa,CAAS;IACtB,UAAU,CAAS;IACnB,UAAU,CAAS;IACnB,QAAQ,CAAQ;IAChB,UAAU,CAAS;IACnB,KAAK,CAAS;IACd,KAAK,CAAS;IACd,IAAI,CAAS;IACb,OAAO,CAAS;IAChB,aAAa,CAAS;IACtB,MAAM,CAAS;IACf,GAAG,CAAQ;IACX,KAAK,CAAQ;IACb,YAAY,CAAQ;IACpB,KAAK,CAAQ;IACb,KAAK,CAAQ;IACb,KAAK,CAAQ;IACb,KAAK,CAAS;IAEd,YAAY,MAAkB,EAAE;QAC9B,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;YAClB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAA;QACpB,CAAC,CAAA;QAED,KAAK,CAAC,GAAG,CAAC,CAAA;QAEV,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAA;QAE9B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAA;QACzC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QAExB,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC/D,uBAAuB;YACvB,IACE,OAAO,GAAG,CAAC,GAAG,KAAK,QAAQ;gBAC3B,OAAO,GAAG,CAAC,GAAG,KAAK,QAAQ,EAC3B,CAAC;gBACD,MAAM,IAAI,SAAS,CACjB,6CAA6C,CAC9C,CAAA;YACH,CAAC;YACD,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;gBACtB,MAAM,IAAI,SAAS,CACjB,gEAAgE,CACjE,CAAA;YACH,CAAC;YACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;YAClB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACtB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,GAAG,SAAS,CAAA;YACpB,IAAI,CAAC,GAAG,GAAG,SAAS,CAAA;YACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACvB,CAAC;QAED,wBAAwB;QACxB,IACE,GAAG,CAAC,aAAa,KAAK,SAAS;YAC/B,OAAO,GAAG,CAAC,GAAG,KAAK,QAAQ,EAC3B,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CACrB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CACzC,CAAA;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAA;QAC1C,CAAC;QAED,IAAI,CAAC,UAAU;YACb,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvD,OAAO,CAAC,MAAM,EAAE;gBAClB,CAAC,CAAC,SAAS,CAAA;QACb,IAAI,CAAC,UAAU;YACb,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvD,OAAO,CAAC,MAAM,EAAE;gBAClB,CAAC,CAAC,SAAS,CAAA;QAEb,iDAAiD;QACjD,+CAA+C;QAC/C,IAAI,CAAC,QAAQ;YACX,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;gBAChC,GAAG,CAAC,QAAQ;gBACd,CAAC,CAAC,iBAAiB,CAAA;QAErB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,KAAK,IAAI,CAAA;QAEzC,0DAA0D;QAC1D,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,SAAS,CAAA;QAErC,qEAAqE;QACrE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QAExB,+BAA+B;QAC/B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAA;QAEtB,8CAA8C;QAC9C,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAA;QAE5B,kEAAkE;QAClE,kEAAkE;QAClE,iCAAiC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAA;QAExC,mEAAmE;QACnE,8DAA8D;QAC9D,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAE1B,IAAI,CAAC,GAAG,GAAG,oBAAoB,CAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CACvC,CAAA;QACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACnC,+DAA+D;QAC/D,IAAI,CAAC,YAAY;YACf,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY;oBACzD,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACnB,IAAI,CAAC,KAAK;YACR,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAA;QAE/D,2CAA2C;QAC3C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAA;QAC9C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAA;QAE9C,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;IACjD,CAAC;IAED,iEAAiE;IACjE,gEAAgE;IAChE,qCAAqC;IACrC,IAAI,CAAC,IAAY,EAAE,GAAmB,EAAE,OAAiB,EAAE;QACzD,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QAC1B,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;IACpC,CAAC;IAED,CAAC,UAAU,CAAC;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAClB,CAAC;IACH,CAAC;IAED,CAAC,SAAS,CAAC,CAAC,KAAgB;QAC1B,MAAM,CAAC,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC1C,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE1B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAA;YACd,CAAC;YACD,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC1B,MAAM,SAAS,GAAG,oBAAoB,CACpC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CACvB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBACZ,IAAI,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACnC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACxD,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YACD,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC3B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC9B,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,EAAE;gBACpD,KAAK;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,KAAK,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IACE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACpB,oBAAoB;gBACpB,CAAC,SAAS,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EACnD,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,oBAAoB,EAAE;oBACjD,KAAK;oBACL,IAAI,EAAE,CAAC;iBACR,CAAC,CAAA;gBACF,OAAO,KAAK,CAAA;YACd,CAAC;YAED,qBAAqB;YACrB,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;YAC7C,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CACP,gBAAgB,EAChB,aAAa,IAAI,qBAAqB,EACtC;oBACE,KAAK;oBACL,IAAI,EAAE,CAAC;iBACR,CACF,CAAA;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,KAAK,CAAC,QAAQ,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;QACjE,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,QAAQ,GAAG,oBAAoB,CACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CACnC,CAAA;QACH,CAAC;QAED,sEAAsE;QACtE,wEAAwE;QACxE,qDAAqD;QACrD,wCAAwC;QACxC,IACE,CAAC,IAAI,CAAC,aAAa;YACnB,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ;YAClC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC;YAC5C,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,EAC3B,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,gCAAgC,EAAE;gBAC7D,KAAK;gBACL,IAAI,EAAE,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtC,YAAY,EAAE,KAAK,CAAC,QAAQ;gBAC5B,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QACd,CAAC;QACD,oBAAoB;QAEpB,oEAAoE;QACpE,mEAAmE;QACnE,IACE,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG;YAC3B,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,KAAK,CAAC,IAAI,KAAK,YAAY,EAC3B,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAA;YAChE,KAAK,CAAC,QAAQ;gBACZ,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;YAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACpD,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;QAChE,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,KAAgB;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC,MAAM,EAAE,CAAA;QACvB,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,OAAO,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAE7C,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,WAAW,CAAC;YACjB,KAAK,YAAY;gBACf,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;oBACf,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAA;gBACjC,CAAC;YAEH,0CAA0C;YAC1C,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,gBAAgB,CAAC;YACtB,KAAK,MAAM,CAAC;YACZ,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAA;YAE7B,KAAK,iBAAiB,CAAC;YACvB,KAAK,aAAa,CAAC;YACnB,KAAK,MAAM,CAAC;YACZ;gBACE,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAA;QACnC,CAAC;IACH,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,EAAS,EAAE,KAAgB;QACnC,2DAA2D;QAC3D,6DAA6D;QAC7D,mCAAmC;QACnC,IAAI,EAAE,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;YAC3C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YACd,KAAK,CAAC,MAAM,EAAE,CAAA;QAChB,CAAC;IACH,CAAC;IAED,CAAC,KAAK,CAAC,CACL,GAAW,EACX,IAAY,EACZ,EAAmD;QAEnD,KAAK,CACH,oBAAoB,CAAC,GAAG,CAAC,EACzB;YACE,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,QAAQ,EAAE,IAAI,CAAC,aAAa;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,QAAQ;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,IAAI;SACX,EACD,EAAE,CACH,CAAA;IACH,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,KAAgB;QACxB,mEAAmE;QACnE,4DAA4D;QAC5D,OAAO,CACL,IAAI,CAAC,UAAU;YACf,CAAC,IAAI,CAAC,aAAa;gBACjB,CAAC,CAAC,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ;oBAC7B,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;oBAC9B,CAAC,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ;wBAC5B,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACtC,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ;gBAC3B,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;YAC/B,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,CAC/D,CAAA;IACH,CAAC;IAED,CAAC,GAAG,CAAC,CAAC,KAAgB;QACpB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;IACrD,CAAC;IAED,CAAC,GAAG,CAAC,CAAC,KAAgB;QACpB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;IACrD,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,KAAgB,EAAE,SAAqB;QAC5C,MAAM,IAAI,GACR,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,GAAG,MAAM;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QACd,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACzD,0CAA0C;YAC1C,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI,CAAW;YACzC,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,KAAK;SACjB,CAAC,CAAA;QACF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAS,EAAE,EAAE;YAC/B,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;gBACd,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;YAC/B,CAAC;YAED,wDAAwD;YACxD,2DAA2D;YAC3D,oCAAoC;YACpC,MAAM,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,CAAA;YACzB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YACxB,SAAS,EAAE,CAAA;QACb,CAAC,CAAC,CAAA;QAEF,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,MAAM,IAAI,GAAG,CAAC,EAAiB,EAAE,EAAE;YACjC,IAAI,EAAE,EAAE,CAAC;gBACP,yDAAyD;gBACzD,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;oBACd,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;gBAC/B,CAAC;gBACD,oBAAoB;gBAEpB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;gBACxB,SAAS,EAAE,CAAA;gBACX,OAAM;YACR,CAAC;YAED,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;gBACpB,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;oBAC5B,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;wBACvB,IAAI,EAAE,EAAE,CAAC;4BACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;wBAC1B,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;wBAChB,CAAC;wBACD,SAAS,EAAE,CAAA;oBACb,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACvB,+BAA+B;YAC/B,gDAAgD;YAChD,wBAAwB;YACxB,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAClC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAA;YAEpB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3D,OAAO,EAAE,CAAA;gBACT,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,CAAA;gBACvC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;gBACzB,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAChC,EAAE,CAAC,CAAC;oBACF,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;oBACtD,CAAC,CAAC,IAAI,EAAE,CACT,CAAA;YACH,CAAC;YAED,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,OAAO,EAAE,CAAA;gBACT,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;oBACvD,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAC3B,EAAE,CAAC,CAAC;wBACF,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;wBACjD,CAAC,CAAC,IAAI,EAAE,CACT,CAAA;gBACH,CAAC;YACH,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC,CAAC,CAAA;QAEF,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAClE,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;YACjB,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAS,EAAE,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;gBACxB,SAAS,EAAE,CAAA;YACb,CAAC,CAAC,CAAA;YACF,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAChB,CAAC;QACD,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACjB,CAAC;IAED,CAAC,SAAS,CAAC,CAAC,KAAgB,EAAE,SAAqB;QACjD,MAAM,IAAI,GACR,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,GAAG,MAAM;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QACd,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE;YAC7C,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;gBACxB,SAAS,EAAE,CAAA;gBACX,OAAM;YACR,CAAC;YAED,IAAI,OAAO,GAAG,CAAC,CAAA;YACf,MAAM,IAAI,GAAG,GAAG,EAAE;gBAChB,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;oBACpB,SAAS,EAAE,CAAA;oBACX,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;oBACd,KAAK,CAAC,MAAM,EAAE,CAAA;gBAChB,CAAC;YACH,CAAC,CAAA;YAED,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjC,OAAO,EAAE,CAAA;gBACT,EAAE,CAAC,MAAM,CACP,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,EACzB,KAAK,CAAC,KAAK,EACX,IAAI,CACL,CAAA;YACH,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,EAAE,CAAA;gBACT,EAAE,CAAC,KAAK,CACN,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EACxB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EACxB,IAAI,CACL,CAAA;YACH,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,WAAW,CAAC,CAAC,KAAgB;QAC5B,KAAK,CAAC,WAAW,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,IAAI,CACP,uBAAuB,EACvB,2BAA2B,KAAK,CAAC,IAAI,EAAE,EACvC,EAAE,KAAK,EAAE,CACV,CAAA;QACD,KAAK,CAAC,MAAM,EAAE,CAAA;IAChB,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,KAAgB,EAAE,IAAgB;QAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,KAAgB,EAAE,IAAgB;QAC3C,MAAM,QAAQ,GAAG,oBAAoB,CACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAC/C,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED,CAAC,IAAI,CAAC;QACJ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;QACf,IAAI,CAAC,UAAU,CAAC,EAAE,CAAA;IACpB,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,KAAgB;QACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,KAAK,CAAC,MAAM,EAAE,CAAA;IAChB,CAAC;IAED,gEAAgE;IAChE,qDAAqD;IACrD,wEAAwE;IACxE,CAAC,UAAU,CAAC,CAAC,KAAgB,EAAE,EAAS;QACtC,OAAO,CACL,KAAK,CAAC,IAAI,KAAK,MAAM;YACrB,CAAC,IAAI,CAAC,MAAM;YACZ,EAAE,CAAC,MAAM,EAAE;YACX,EAAE,CAAC,KAAK,IAAI,CAAC;YACb,CAAC,SAAS,CACX,CAAA;IACH,CAAC;IAED,0DAA0D;IAC1D,CAAC,OAAO,CAAC,CAAC,KAAgB;QACxB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;QACZ,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC5B,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAC5B,CAAA;IACH,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,KAAgB;QAC3B,uEAAuE;QACvE,kEAAkE;QAClE,uEAAuE;QACvE,0BAA0B;QAC1B,oEAAoE;QACpE,wEAAwE;QACxE,0EAA0E;QAC1E,2EAA2E;QAC3E,yEAAyE;QACzE,iDAAiD;QACjD,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC1B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACtC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,KAAgB,EAAE,SAA+B;QAC1D,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAA;QAEvB,MAAM,IAAI,GAAG,CAAC,EAAU,EAAE,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAA;YACvB,SAAS,CAAC,EAAE,CAAC,CAAA;QACf,CAAC,CAAA;QAED,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;gBACrC,IAAI,EAAE,EAAE,CAAC;oBACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;oBACxB,IAAI,EAAE,CAAA;oBACN,OAAM;gBACR,CAAC;gBACD,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAA;gBACxB,KAAK,EAAE,CAAA;YACT,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,MAAM,KAAK,GAAG,GAAG,EAAE;YACjB,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,oBAAoB,CACjC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CACrC,CAAA;gBACD,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;oBACxB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;wBAC1C,IAAI,EAAE,EAAE,CAAC;4BACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;4BACxB,IAAI,EAAE,CAAA;4BACN,OAAM;wBACR,CAAC;wBACD,eAAe,EAAE,CAAA;oBACnB,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;YACD,eAAe,EAAE,CAAA;QACnB,CAAC,CAAA;QAED,MAAM,eAAe,GAAG,GAAG,EAAE;YAC3B,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE;gBAC/C,IACE,EAAE;oBACF,CAAC,IAAI,CAAC,IAAI;wBACR,oBAAoB;wBACpB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EACvD,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;oBACjB,IAAI,EAAE,CAAA;oBACN,OAAM;gBACR,CAAC;gBACD,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;oBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACxC,CAAC;gBAED,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrB,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBAC/B,MAAM,SAAS,GACb,IAAI,CAAC,KAAK;4BACV,KAAK,CAAC,IAAI;4BACV,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI,CAAA;wBACnC,MAAM,UAAU,GAAG,CAAC,EAA6B,EAAE,EAAE,CACnD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;wBACvC,IAAI,CAAC,SAAS,EAAE,CAAC;4BACf,OAAO,UAAU,EAAE,CAAA;wBACrB,CAAC;wBACD,OAAO,EAAE,CAAC,KAAK,CACb,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAClB,UAAU,CACX,CAAA;oBACH,CAAC;oBACD,sCAAsC;oBACtC,2DAA2D;oBAC3D,sDAAsD;oBACtD,0DAA0D;oBAC1D,2DAA2D;oBAC3D,2DAA2D;oBAC3D,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;wBAChC,OAAO,EAAE,CAAC,KAAK,CACb,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,CAAC,EAAiB,EAAE,EAAE,CACpB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CACxC,CAAA;oBACH,CAAC;gBACH,CAAC;gBAED,8BAA8B;gBAC9B,8CAA8C;gBAC9C,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;oBAChC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;gBACxC,CAAC;gBAED,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAE,CACtC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CACtC,CAAA;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACtB,KAAK,EAAE,CAAA;QACT,CAAC;aAAM,CAAC;YACN,QAAQ,EAAE,CAAA;QACZ,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC,CACN,EAA4B,EAC5B,KAAgB,EAChB,IAAgB;QAEhB,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YACxB,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YAEhC,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YAEpC,KAAK,cAAc;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YAEnC,KAAK,WAAW,CAAC;YACjB,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACvC,CAAC;IACH,CAAC;IAED,CAAC,IAAI,CAAC,CACJ,KAAgB,EAChB,QAAgB,EAChB,IAAwB,EACxB,IAAgB;QAEhB,0DAA0D;QAC1D,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAE;YAC9C,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;gBACd,KAAK,CAAC,MAAM,EAAE,CAAA;YAChB,CAAC;YACD,IAAI,EAAE,CAAA;QACR,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAED,MAAM,QAAQ,GAAG,CAAC,EAAa,EAAE,EAAE;IACjC,IAAI,CAAC;QACH,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;IACrB,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;IACnB,CAAC;AACH,CAAC,CAAA;AAED,MAAM,OAAO,UAAW,SAAQ,MAAM;IACpC,IAAI,GAAS,IAAI,CAAC;IAElB,CAAC,MAAM,CAAC,CAAC,EAA4B,EAAE,KAAgB;QACrD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAA;IAC3C,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,KAAgB;QACxB,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAA;QAEvB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACvB,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5C,IAAI,EAAE,EAAE,CAAC;gBACP,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAW,EAAE,KAAK,CAAC,CAAA;YAC1C,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAA;QAC1B,CAAC;QAED,mEAAmE;QACnE,4BAA4B;QAC5B,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,oBAAoB,CACjC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CACrC,CAAA;YACD,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;gBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChD,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAiB,EAAE,KAAK,CAAC,CAAA;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,CAClC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CACrC,CAAA;QACD,IACE,EAAE;YACF,CAAC,IAAI,CAAC,IAAI;gBACR,oBAAoB;gBACpB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EACvD,CAAC;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAClC,CAAC;QAED,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;YACrB,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC/B,MAAM,SAAS,GACb,IAAI,CAAC,KAAK;oBACV,KAAK,CAAC,IAAI;oBACV,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI,CAAA;gBACnC,MAAM,CAAC,EAAE,CAAC,GACR,SAAS,CAAC,CAAC;oBACT,QAAQ,CAAC,GAAG,EAAE;wBACZ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC1D,CAAC,CAAC;oBACJ,CAAC,CAAC,EAAE,CAAA;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YAChC,CAAC;YACD,qCAAqC;YACrC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,CACzB,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CACrC,CAAA;YACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;QACzB,CAAC;QAED,+BAA+B;QAC/B,0DAA0D;QAC1D,MAAM,CAAC,EAAE,CAAC,GACR,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3B,EAAE;YACJ,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC1D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;IACzB,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,KAAgB,EAAE,IAAgB;QACvC,MAAM,IAAI,GACR,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,GAAG,MAAM;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QAEd,MAAM,IAAI,GAAG,CAAC,EAA6B,EAAE,EAAE;YAC7C,IAAI,UAAU,CAAA;YACd,IAAI,CAAC;gBACH,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;YAClB,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,UAAU,GAAG,CAAC,CAAA;YAChB,CAAC;YACD,IAAI,EAAE,IAAI,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,CAAE,EAAY,IAAI,UAAU,EAAE,KAAK,CAAC,CAAA;YACnD,CAAC;YACD,IAAI,EAAE,CAAA;QACR,CAAC,CAAA;QAED,IAAI,EAAU,CAAA;QACd,IAAI,CAAC;YACH,EAAE,GAAG,EAAE,CAAC,QAAQ,CACd,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EACxB,IAAI,CACL,CAAA;QACH,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,EAAW,CAAC,CAAA;QAC1B,CAAC;QACD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAClE,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;YACjB,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAS,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAChB,CAAC;QAED,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;YAC9B,IAAI,CAAC;gBACH,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAC1C,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACZ,IAAI,CAAC,EAAW,CAAC,CAAA;YACnB,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAChB,IAAI,EAAE,GAAG,IAAI,CAAA;YACb,2CAA2C;YAC3C,0CAA0C;YAC1C,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,CAAA;gBACvC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;gBACzB,IAAI,CAAC;oBACH,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;gBAClC,CAAC;gBAAC,OAAO,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;oBACrD,CAAC;oBAAC,OAAO,QAAQ,EAAE,CAAC;wBAClB,EAAE,GAAG,SAAS,CAAA;oBAChB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;gBAE5B,IAAI,CAAC;oBACH,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC7C,CAAC;gBAAC,OAAO,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC;wBACH,EAAE,CAAC,SAAS,CACV,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,MAAM,CAAC,GAAG,CAAC,EACX,MAAM,CAAC,GAAG,CAAC,CACZ,CAAA;oBACH,CAAC;oBAAC,OAAO,OAAO,EAAE,CAAC;wBACjB,EAAE,GAAG,EAAE,IAAI,QAAQ,CAAA;oBACrB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,EAAW,CAAC,CAAA;QACnB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,SAAS,CAAC,CAAC,KAAgB,EAAE,IAAgB;QAC5C,MAAM,IAAI,GACR,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC9B,KAAK,CAAC,IAAI,GAAG,MAAM;YACrB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;QACd,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA;QACpD,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,OAAO,CAAC,CAAC,EAAW,EAAE,KAAK,CAAC,CAAA;YACjC,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QACD,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,EAAE,CAAC,UAAU,CACX,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,EACzB,KAAK,CAAC,KAAK,CACZ,CAAA;gBACD,oBAAoB;YACtB,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;QACjB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,EAAE,CAAC,SAAS,CACV,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EACxB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CACzB,CAAA;YACH,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;QACjB,CAAC;QACD,IAAI,EAAE,CAAA;QACN,KAAK,CAAC,MAAM,EAAE,CAAA;IAChB,CAAC;IAED,CAAC,KAAK,CAAC,CAAC,GAAW,EAAE,IAAY;QAC/B,IAAI,CAAC;YACH,OAAO,SAAS,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE;gBAC1C,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,QAAQ,EAAE,IAAI,CAAC,aAAa;gBAC5B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,KAAK,EAAE,IAAI,CAAC,QAAQ;gBACpB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,IAAI,EAAE,IAAI;aACX,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAED,CAAC,IAAI,CAAC,CACJ,KAAgB,EAChB,QAAgB,EAChB,IAAwB,EACxB,IAAgB;QAEhB,MAAM,EAAE,GAAyB,GAAG,IAAI,MAAM,CAAA;QAC9C,IAAI,CAAC;YACH,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAA;YACxC,IAAI,EAAE,CAAA;YACN,KAAK,CAAC,MAAM,EAAE,CAAA;QAChB,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EAAW,EAAE,KAAK,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// the PEND/UNPEND stuff tracks whether we're ready to emit end/close yet.\n// but the path reservations are required to avoid race conditions where\n// parallelized unpack ops may mess with one another, due to dependencies\n// (like a Link depending on its target) or destructive operations (like\n// clobbering an fs object to create one of a different type.)\n\nimport * as fsm from '@isaacs/fs-minipass'\nimport assert from 'node:assert'\nimport { randomBytes } from 'node:crypto'\nimport fs, { type Stats } from 'node:fs'\nimport path from 'node:path'\nimport { getWriteFlag } from './get-write-flag.js'\nimport { mkdir, MkdirError, mkdirSync } from './mkdir.js'\nimport { normalizeUnicode } from './normalize-unicode.js'\nimport { normalizeWindowsPath } from './normalize-windows-path.js'\nimport { Parser } from './parse.js'\nimport { stripAbsolutePath } from './strip-absolute-path.js'\nimport { stripTrailingSlashes } from './strip-trailing-slashes.js'\nimport * as wc from './winchars.js'\n\nimport { TarOptions } from './options.js'\nimport { PathReservations } from './path-reservations.js'\nimport { ReadEntry } from './read-entry.js'\nimport { WarnData } from './warn-method.js'\n\nconst ONENTRY = Symbol('onEntry')\nconst CHECKFS = Symbol('checkFs')\nconst CHECKFS2 = Symbol('checkFs2')\nconst PRUNECACHE = Symbol('pruneCache')\nconst ISREUSABLE = Symbol('isReusable')\nconst MAKEFS = Symbol('makeFs')\nconst FILE = Symbol('file')\nconst DIRECTORY = Symbol('directory')\nconst LINK = Symbol('link')\nconst SYMLINK = Symbol('symlink')\nconst HARDLINK = Symbol('hardlink')\nconst UNSUPPORTED = Symbol('unsupported')\nconst CHECKPATH = Symbol('checkPath')\nconst MKDIR = Symbol('mkdir')\nconst ONERROR = Symbol('onError')\nconst PENDING = Symbol('pending')\nconst PEND = Symbol('pend')\nconst UNPEND = Symbol('unpend')\nconst ENDED = Symbol('ended')\nconst MAYBECLOSE = Symbol('maybeClose')\nconst SKIP = Symbol('skip')\nconst DOCHOWN = Symbol('doChown')\nconst UID = Symbol('uid')\nconst GID = Symbol('gid')\nconst CHECKED_CWD = Symbol('checkedCwd')\nconst platform =\n  process.env.TESTING_TAR_FAKE_PLATFORM || process.platform\nconst isWindows = platform === 'win32'\nconst DEFAULT_MAX_DEPTH = 1024\n\n// Unlinks on Windows are not atomic.\n//\n// This means that if you have a file entry, followed by another\n// file entry with an identical name, and you cannot re-use the file\n// (because it's a hardlink, or because unlink:true is set, or it's\n// Windows, which does not have useful nlink values), then the unlink\n// will be committed to the disk AFTER the new file has been written\n// over the old one, deleting the new file.\n//\n// To work around this, on Windows systems, we rename the file and then\n// delete the renamed file.  It's a sloppy kludge, but frankly, I do not\n// know of a better way to do this, given windows' non-atomic unlink\n// semantics.\n//\n// See: https://github.com/npm/node-tar/issues/183\n/* c8 ignore start */\nconst unlinkFile = (\n  path: string,\n  cb: (er?: Error | null) => void,\n) => {\n  if (!isWindows) {\n    return fs.unlink(path, cb)\n  }\n\n  const name = path + '.DELETE.' + randomBytes(16).toString('hex')\n  fs.rename(path, name, er => {\n    if (er) {\n      return cb(er)\n    }\n    fs.unlink(name, cb)\n  })\n}\n/* c8 ignore stop */\n\n/* c8 ignore start */\nconst unlinkFileSync = (path: string) => {\n  if (!isWindows) {\n    return fs.unlinkSync(path)\n  }\n\n  const name = path + '.DELETE.' + randomBytes(16).toString('hex')\n  fs.renameSync(path, name)\n  fs.unlinkSync(name)\n}\n/* c8 ignore stop */\n\n// this.gid, entry.gid, this.processUid\nconst uint32 = (\n  a: number | undefined,\n  b: number | undefined,\n  c: number | undefined,\n) =>\n  a !== undefined && a === a >>> 0 ? a\n  : b !== undefined && b === b >>> 0 ? b\n  : c\n\n// clear the cache if it's a case-insensitive unicode-squashing match.\n// we can't know if the current file system is case-sensitive or supports\n// unicode fully, so we check for similarity on the maximally compatible\n// representation.  Err on the side of pruning, since all it's doing is\n// preventing lstats, and it's not the end of the world if we get a false\n// positive.\n// Note that on windows, we always drop the entire cache whenever a\n// symbolic link is encountered, because 8.3 filenames are impossible\n// to reason about, and collisions are hazards rather than just failures.\nconst cacheKeyNormalize = (path: string) =>\n  stripTrailingSlashes(\n    normalizeWindowsPath(normalizeUnicode(path)),\n  ).toLowerCase()\n\n// remove all cache entries matching ${abs}/**\nconst pruneCache = (cache: Map<string, boolean>, abs: string) => {\n  abs = cacheKeyNormalize(abs)\n  for (const path of cache.keys()) {\n    const pnorm = cacheKeyNormalize(path)\n    if (pnorm === abs || pnorm.indexOf(abs + '/') === 0) {\n      cache.delete(path)\n    }\n  }\n}\n\nconst dropCache = (cache: Map<string, boolean>) => {\n  for (const key of cache.keys()) {\n    cache.delete(key)\n  }\n}\n\nexport class Unpack extends Parser {\n  [ENDED]: boolean = false;\n  [CHECKED_CWD]: boolean = false;\n  [PENDING]: number = 0\n\n  reservations: PathReservations = new PathReservations()\n  transform?: TarOptions['transform']\n  writable: true = true\n  readable: false = false\n  dirCache: Exclude<TarOptions['dirCache'], undefined>\n  uid?: number\n  gid?: number\n  setOwner: boolean\n  preserveOwner: boolean\n  processGid?: number\n  processUid?: number\n  maxDepth: number\n  forceChown: boolean\n  win32: boolean\n  newer: boolean\n  keep: boolean\n  noMtime: boolean\n  preservePaths: boolean\n  unlink: boolean\n  cwd: string\n  strip: number\n  processUmask: number\n  umask: number\n  dmode: number\n  fmode: number\n  chmod: boolean\n\n  constructor(opt: TarOptions = {}) {\n    opt.ondone = () => {\n      this[ENDED] = true\n      this[MAYBECLOSE]()\n    }\n\n    super(opt)\n\n    this.transform = opt.transform\n\n    this.dirCache = opt.dirCache || new Map()\n    this.chmod = !!opt.chmod\n\n    if (typeof opt.uid === 'number' || typeof opt.gid === 'number') {\n      // need both or neither\n      if (\n        typeof opt.uid !== 'number' ||\n        typeof opt.gid !== 'number'\n      ) {\n        throw new TypeError(\n          'cannot set owner without number uid and gid',\n        )\n      }\n      if (opt.preserveOwner) {\n        throw new TypeError(\n          'cannot preserve owner in archive and also set owner explicitly',\n        )\n      }\n      this.uid = opt.uid\n      this.gid = opt.gid\n      this.setOwner = true\n    } else {\n      this.uid = undefined\n      this.gid = undefined\n      this.setOwner = false\n    }\n\n    // default true for root\n    if (\n      opt.preserveOwner === undefined &&\n      typeof opt.uid !== 'number'\n    ) {\n      this.preserveOwner = !!(\n        process.getuid && process.getuid() === 0\n      )\n    } else {\n      this.preserveOwner = !!opt.preserveOwner\n    }\n\n    this.processUid =\n      (this.preserveOwner || this.setOwner) && process.getuid ?\n        process.getuid()\n      : undefined\n    this.processGid =\n      (this.preserveOwner || this.setOwner) && process.getgid ?\n        process.getgid()\n      : undefined\n\n    // prevent excessively deep nesting of subfolders\n    // set to `Infinity` to remove this restriction\n    this.maxDepth =\n      typeof opt.maxDepth === 'number' ?\n        opt.maxDepth\n      : DEFAULT_MAX_DEPTH\n\n    // mostly just for testing, but useful in some cases.\n    // Forcibly trigger a chown on every entry, no matter what\n    this.forceChown = opt.forceChown === true\n\n    // turn ><?| in filenames into 0xf000-higher encoded forms\n    this.win32 = !!opt.win32 || isWindows\n\n    // do not unpack over files that are newer than what's in the archive\n    this.newer = !!opt.newer\n\n    // do not unpack over ANY files\n    this.keep = !!opt.keep\n\n    // do not set mtime/atime of extracted entries\n    this.noMtime = !!opt.noMtime\n\n    // allow .., absolute path entries, and unpacking through symlinks\n    // without this, warn and skip .., relativize absolutes, and error\n    // on symlinks in extraction path\n    this.preservePaths = !!opt.preservePaths\n\n    // unlink files and links before writing. This breaks existing hard\n    // links, and removes symlink directories rather than erroring\n    this.unlink = !!opt.unlink\n\n    this.cwd = normalizeWindowsPath(\n      path.resolve(opt.cwd || process.cwd()),\n    )\n    this.strip = Number(opt.strip) || 0\n    // if we're not chmodding, then we don't need the process umask\n    this.processUmask =\n      !this.chmod ? 0\n      : typeof opt.processUmask === 'number' ? opt.processUmask\n      : process.umask()\n    this.umask =\n      typeof opt.umask === 'number' ? opt.umask : this.processUmask\n\n    // default mode for dirs created as parents\n    this.dmode = opt.dmode || 0o0777 & ~this.umask\n    this.fmode = opt.fmode || 0o0666 & ~this.umask\n\n    this.on('entry', entry => this[ONENTRY](entry))\n  }\n\n  // a bad or damaged archive is a warning for Parser, but an error\n  // when extracting.  Mark those errors as unrecoverable, because\n  // the Unpack contract cannot be met.\n  warn(code: string, msg: string | Error, data: WarnData = {}) {\n    if (code === 'TAR_BAD_ARCHIVE' || code === 'TAR_ABORT') {\n      data.recoverable = false\n    }\n    return super.warn(code, msg, data)\n  }\n\n  [MAYBECLOSE]() {\n    if (this[ENDED] && this[PENDING] === 0) {\n      this.emit('prefinish')\n      this.emit('finish')\n      this.emit('end')\n    }\n  }\n\n  [CHECKPATH](entry: ReadEntry) {\n    const p = normalizeWindowsPath(entry.path)\n    const parts = p.split('/')\n\n    if (this.strip) {\n      if (parts.length < this.strip) {\n        return false\n      }\n      if (entry.type === 'Link') {\n        const linkparts = normalizeWindowsPath(\n          String(entry.linkpath),\n        ).split('/')\n        if (linkparts.length >= this.strip) {\n          entry.linkpath = linkparts.slice(this.strip).join('/')\n        } else {\n          return false\n        }\n      }\n      parts.splice(0, this.strip)\n      entry.path = parts.join('/')\n    }\n\n    if (isFinite(this.maxDepth) && parts.length > this.maxDepth) {\n      this.warn('TAR_ENTRY_ERROR', 'path excessively deep', {\n        entry,\n        path: p,\n        depth: parts.length,\n        maxDepth: this.maxDepth,\n      })\n      return false\n    }\n\n    if (!this.preservePaths) {\n      if (\n        parts.includes('..') ||\n        /* c8 ignore next */\n        (isWindows && /^[a-z]:\\.\\.$/i.test(parts[0] ?? ''))\n      ) {\n        this.warn('TAR_ENTRY_ERROR', `path contains '..'`, {\n          entry,\n          path: p,\n        })\n        return false\n      }\n\n      // strip off the root\n      const [root, stripped] = stripAbsolutePath(p)\n      if (root) {\n        entry.path = String(stripped)\n        this.warn(\n          'TAR_ENTRY_INFO',\n          `stripping ${root} from absolute path`,\n          {\n            entry,\n            path: p,\n          },\n        )\n      }\n    }\n\n    if (path.isAbsolute(entry.path)) {\n      entry.absolute = normalizeWindowsPath(path.resolve(entry.path))\n    } else {\n      entry.absolute = normalizeWindowsPath(\n        path.resolve(this.cwd, entry.path),\n      )\n    }\n\n    // if we somehow ended up with a path that escapes the cwd, and we are\n    // not in preservePaths mode, then something is fishy!  This should have\n    // been prevented above, so ignore this for coverage.\n    /* c8 ignore start - defense in depth */\n    if (\n      !this.preservePaths &&\n      typeof entry.absolute === 'string' &&\n      entry.absolute.indexOf(this.cwd + '/') !== 0 &&\n      entry.absolute !== this.cwd\n    ) {\n      this.warn('TAR_ENTRY_ERROR', 'path escaped extraction target', {\n        entry,\n        path: normalizeWindowsPath(entry.path),\n        resolvedPath: entry.absolute,\n        cwd: this.cwd,\n      })\n      return false\n    }\n    /* c8 ignore stop */\n\n    // an archive can set properties on the extraction directory, but it\n    // may not replace the cwd with a different kind of thing entirely.\n    if (\n      entry.absolute === this.cwd &&\n      entry.type !== 'Directory' &&\n      entry.type !== 'GNUDumpDir'\n    ) {\n      return false\n    }\n\n    // only encode : chars that aren't drive letter indicators\n    if (this.win32) {\n      const { root: aRoot } = path.win32.parse(String(entry.absolute))\n      entry.absolute =\n        aRoot + wc.encode(String(entry.absolute).slice(aRoot.length))\n      const { root: pRoot } = path.win32.parse(entry.path)\n      entry.path = pRoot + wc.encode(entry.path.slice(pRoot.length))\n    }\n\n    return true\n  }\n\n  [ONENTRY](entry: ReadEntry) {\n    if (!this[CHECKPATH](entry)) {\n      return entry.resume()\n    }\n\n    assert.equal(typeof entry.absolute, 'string')\n\n    switch (entry.type) {\n      case 'Directory':\n      case 'GNUDumpDir':\n        if (entry.mode) {\n          entry.mode = entry.mode | 0o700\n        }\n\n      // eslint-disable-next-line no-fallthrough\n      case 'File':\n      case 'OldFile':\n      case 'ContiguousFile':\n      case 'Link':\n      case 'SymbolicLink':\n        return this[CHECKFS](entry)\n\n      case 'CharacterDevice':\n      case 'BlockDevice':\n      case 'FIFO':\n      default:\n        return this[UNSUPPORTED](entry)\n    }\n  }\n\n  [ONERROR](er: Error, entry: ReadEntry) {\n    // Cwd has to exist, or else nothing works. That's serious.\n    // Other errors are warnings, which raise the error in strict\n    // mode, but otherwise continue on.\n    if (er.name === 'CwdError') {\n      this.emit('error', er)\n    } else {\n      this.warn('TAR_ENTRY_ERROR', er, { entry })\n      this[UNPEND]()\n      entry.resume()\n    }\n  }\n\n  [MKDIR](\n    dir: string,\n    mode: number,\n    cb: (er?: null | MkdirError, made?: string) => void,\n  ) {\n    mkdir(\n      normalizeWindowsPath(dir),\n      {\n        uid: this.uid,\n        gid: this.gid,\n        processUid: this.processUid,\n        processGid: this.processGid,\n        umask: this.processUmask,\n        preserve: this.preservePaths,\n        unlink: this.unlink,\n        cache: this.dirCache,\n        cwd: this.cwd,\n        mode: mode,\n      },\n      cb,\n    )\n  }\n\n  [DOCHOWN](entry: ReadEntry) {\n    // in preserve owner mode, chown if the entry doesn't match process\n    // in set owner mode, chown if setting doesn't match process\n    return (\n      this.forceChown ||\n      (this.preserveOwner &&\n        ((typeof entry.uid === 'number' &&\n          entry.uid !== this.processUid) ||\n          (typeof entry.gid === 'number' &&\n            entry.gid !== this.processGid))) ||\n      (typeof this.uid === 'number' &&\n        this.uid !== this.processUid) ||\n      (typeof this.gid === 'number' && this.gid !== this.processGid)\n    )\n  }\n\n  [UID](entry: ReadEntry) {\n    return uint32(this.uid, entry.uid, this.processUid)\n  }\n\n  [GID](entry: ReadEntry) {\n    return uint32(this.gid, entry.gid, this.processGid)\n  }\n\n  [FILE](entry: ReadEntry, fullyDone: () => void) {\n    const mode =\n      typeof entry.mode === 'number' ?\n        entry.mode & 0o7777\n      : this.fmode\n    const stream = new fsm.WriteStream(String(entry.absolute), {\n      // slight lie, but it can be numeric flags\n      flags: getWriteFlag(entry.size) as string,\n      mode: mode,\n      autoClose: false,\n    })\n    stream.on('error', (er: Error) => {\n      if (stream.fd) {\n        fs.close(stream.fd, () => {})\n      }\n\n      // flush all the data out so that we aren't left hanging\n      // if the error wasn't actually fatal.  otherwise the parse\n      // is blocked, and we never proceed.\n      stream.write = () => true\n      this[ONERROR](er, entry)\n      fullyDone()\n    })\n\n    let actions = 1\n    const done = (er?: null | Error) => {\n      if (er) {\n        /* c8 ignore start - we should always have a fd by now */\n        if (stream.fd) {\n          fs.close(stream.fd, () => {})\n        }\n        /* c8 ignore stop */\n\n        this[ONERROR](er, entry)\n        fullyDone()\n        return\n      }\n\n      if (--actions === 0) {\n        if (stream.fd !== undefined) {\n          fs.close(stream.fd, er => {\n            if (er) {\n              this[ONERROR](er, entry)\n            } else {\n              this[UNPEND]()\n            }\n            fullyDone()\n          })\n        }\n      }\n    }\n\n    stream.on('finish', () => {\n      // if futimes fails, try utimes\n      // if utimes fails, fail with the original error\n      // same for fchown/chown\n      const abs = String(entry.absolute)\n      const fd = stream.fd\n\n      if (typeof fd === 'number' && entry.mtime && !this.noMtime) {\n        actions++\n        const atime = entry.atime || new Date()\n        const mtime = entry.mtime\n        fs.futimes(fd, atime, mtime, er =>\n          er ?\n            fs.utimes(abs, atime, mtime, er2 => done(er2 && er))\n          : done(),\n        )\n      }\n\n      if (typeof fd === 'number' && this[DOCHOWN](entry)) {\n        actions++\n        const uid = this[UID](entry)\n        const gid = this[GID](entry)\n        if (typeof uid === 'number' && typeof gid === 'number') {\n          fs.fchown(fd, uid, gid, er =>\n            er ?\n              fs.chown(abs, uid, gid, er2 => done(er2 && er))\n            : done(),\n          )\n        }\n      }\n\n      done()\n    })\n\n    const tx = this.transform ? this.transform(entry) || entry : entry\n    if (tx !== entry) {\n      tx.on('error', (er: Error) => {\n        this[ONERROR](er, entry)\n        fullyDone()\n      })\n      entry.pipe(tx)\n    }\n    tx.pipe(stream)\n  }\n\n  [DIRECTORY](entry: ReadEntry, fullyDone: () => void) {\n    const mode =\n      typeof entry.mode === 'number' ?\n        entry.mode & 0o7777\n      : this.dmode\n    this[MKDIR](String(entry.absolute), mode, er => {\n      if (er) {\n        this[ONERROR](er, entry)\n        fullyDone()\n        return\n      }\n\n      let actions = 1\n      const done = () => {\n        if (--actions === 0) {\n          fullyDone()\n          this[UNPEND]()\n          entry.resume()\n        }\n      }\n\n      if (entry.mtime && !this.noMtime) {\n        actions++\n        fs.utimes(\n          String(entry.absolute),\n          entry.atime || new Date(),\n          entry.mtime,\n          done,\n        )\n      }\n\n      if (this[DOCHOWN](entry)) {\n        actions++\n        fs.chown(\n          String(entry.absolute),\n          Number(this[UID](entry)),\n          Number(this[GID](entry)),\n          done,\n        )\n      }\n\n      done()\n    })\n  }\n\n  [UNSUPPORTED](entry: ReadEntry) {\n    entry.unsupported = true\n    this.warn(\n      'TAR_ENTRY_UNSUPPORTED',\n      `unsupported entry type: ${entry.type}`,\n      { entry },\n    )\n    entry.resume()\n  }\n\n  [SYMLINK](entry: ReadEntry, done: () => void) {\n    this[LINK](entry, String(entry.linkpath), 'symlink', done)\n  }\n\n  [HARDLINK](entry: ReadEntry, done: () => void) {\n    const linkpath = normalizeWindowsPath(\n      path.resolve(this.cwd, String(entry.linkpath)),\n    )\n    this[LINK](entry, linkpath, 'link', done)\n  }\n\n  [PEND]() {\n    this[PENDING]++\n  }\n\n  [UNPEND]() {\n    this[PENDING]--\n    this[MAYBECLOSE]()\n  }\n\n  [SKIP](entry: ReadEntry) {\n    this[UNPEND]()\n    entry.resume()\n  }\n\n  // Check if we can reuse an existing filesystem entry safely and\n  // overwrite it, rather than unlinking and recreating\n  // Windows doesn't report a useful nlink, so we just never reuse entries\n  [ISREUSABLE](entry: ReadEntry, st: Stats) {\n    return (\n      entry.type === 'File' &&\n      !this.unlink &&\n      st.isFile() &&\n      st.nlink <= 1 &&\n      !isWindows\n    )\n  }\n\n  // check if a thing is there, and if so, try to clobber it\n  [CHECKFS](entry: ReadEntry) {\n    this[PEND]()\n    const paths = [entry.path]\n    if (entry.linkpath) {\n      paths.push(entry.linkpath)\n    }\n    this.reservations.reserve(paths, done =>\n      this[CHECKFS2](entry, done),\n    )\n  }\n\n  [PRUNECACHE](entry: ReadEntry) {\n    // if we are not creating a directory, and the path is in the dirCache,\n    // then that means we are about to delete the directory we created\n    // previously, and it is no longer going to be a directory, and neither\n    // is any of its children.\n    // If a symbolic link is encountered, all bets are off.  There is no\n    // reasonable way to sanitize the cache in such a way we will be able to\n    // avoid having filesystem collisions.  If this happens with a non-symlink\n    // entry, it'll just fail to unpack, but a symlink to a directory, using an\n    // 8.3 shortname or certain unicode attacks, can evade detection and lead\n    // to arbitrary writes to anywhere on the system.\n    if (entry.type === 'SymbolicLink') {\n      dropCache(this.dirCache)\n    } else if (entry.type !== 'Directory') {\n      pruneCache(this.dirCache, String(entry.absolute))\n    }\n  }\n\n  [CHECKFS2](entry: ReadEntry, fullyDone: (er?: Error) => void) {\n    this[PRUNECACHE](entry)\n\n    const done = (er?: Error) => {\n      this[PRUNECACHE](entry)\n      fullyDone(er)\n    }\n\n    const checkCwd = () => {\n      this[MKDIR](this.cwd, this.dmode, er => {\n        if (er) {\n          this[ONERROR](er, entry)\n          done()\n          return\n        }\n        this[CHECKED_CWD] = true\n        start()\n      })\n    }\n\n    const start = () => {\n      if (entry.absolute !== this.cwd) {\n        const parent = normalizeWindowsPath(\n          path.dirname(String(entry.absolute)),\n        )\n        if (parent !== this.cwd) {\n          return this[MKDIR](parent, this.dmode, er => {\n            if (er) {\n              this[ONERROR](er, entry)\n              done()\n              return\n            }\n            afterMakeParent()\n          })\n        }\n      }\n      afterMakeParent()\n    }\n\n    const afterMakeParent = () => {\n      fs.lstat(String(entry.absolute), (lstatEr, st) => {\n        if (\n          st &&\n          (this.keep ||\n            /* c8 ignore next */\n            (this.newer && st.mtime > (entry.mtime ?? st.mtime)))\n        ) {\n          this[SKIP](entry)\n          done()\n          return\n        }\n        if (lstatEr || this[ISREUSABLE](entry, st)) {\n          return this[MAKEFS](null, entry, done)\n        }\n\n        if (st.isDirectory()) {\n          if (entry.type === 'Directory') {\n            const needChmod =\n              this.chmod &&\n              entry.mode &&\n              (st.mode & 0o7777) !== entry.mode\n            const afterChmod = (er?: Error | null | undefined) =>\n              this[MAKEFS](er ?? null, entry, done)\n            if (!needChmod) {\n              return afterChmod()\n            }\n            return fs.chmod(\n              String(entry.absolute),\n              Number(entry.mode),\n              afterChmod,\n            )\n          }\n          // Not a dir entry, have to remove it.\n          // NB: the only way to end up with an entry that is the cwd\n          // itself, in such a way that == does not detect, is a\n          // tricky windows absolute path with UNC or 8.3 parts (and\n          // preservePaths:true, or else it will have been stripped).\n          // In that case, the user has opted out of path protections\n          // explicitly, so if they blow away the cwd, c'est la vie.\n          if (entry.absolute !== this.cwd) {\n            return fs.rmdir(\n              String(entry.absolute),\n              (er?: null | Error) =>\n                this[MAKEFS](er ?? null, entry, done),\n            )\n          }\n        }\n\n        // not a dir, and not reusable\n        // don't remove if the cwd, we want that error\n        if (entry.absolute === this.cwd) {\n          return this[MAKEFS](null, entry, done)\n        }\n\n        unlinkFile(String(entry.absolute), er =>\n          this[MAKEFS](er ?? null, entry, done),\n        )\n      })\n    }\n\n    if (this[CHECKED_CWD]) {\n      start()\n    } else {\n      checkCwd()\n    }\n  }\n\n  [MAKEFS](\n    er: null | undefined | Error,\n    entry: ReadEntry,\n    done: () => void,\n  ) {\n    if (er) {\n      this[ONERROR](er, entry)\n      done()\n      return\n    }\n\n    switch (entry.type) {\n      case 'File':\n      case 'OldFile':\n      case 'ContiguousFile':\n        return this[FILE](entry, done)\n\n      case 'Link':\n        return this[HARDLINK](entry, done)\n\n      case 'SymbolicLink':\n        return this[SYMLINK](entry, done)\n\n      case 'Directory':\n      case 'GNUDumpDir':\n        return this[DIRECTORY](entry, done)\n    }\n  }\n\n  [LINK](\n    entry: ReadEntry,\n    linkpath: string,\n    link: 'link' | 'symlink',\n    done: () => void,\n  ) {\n    // XXX: get the type ('symlink' or 'junction') for windows\n    fs[link](linkpath, String(entry.absolute), er => {\n      if (er) {\n        this[ONERROR](er, entry)\n      } else {\n        this[UNPEND]()\n        entry.resume()\n      }\n      done()\n    })\n  }\n}\n\nconst callSync = (fn: () => any) => {\n  try {\n    return [null, fn()]\n  } catch (er) {\n    return [er, null]\n  }\n}\n\nexport class UnpackSync extends Unpack {\n  sync: true = true;\n\n  [MAKEFS](er: null | Error | undefined, entry: ReadEntry) {\n    return super[MAKEFS](er, entry, () => {})\n  }\n\n  [CHECKFS](entry: ReadEntry) {\n    this[PRUNECACHE](entry)\n\n    if (!this[CHECKED_CWD]) {\n      const er = this[MKDIR](this.cwd, this.dmode)\n      if (er) {\n        return this[ONERROR](er as Error, entry)\n      }\n      this[CHECKED_CWD] = true\n    }\n\n    // don't bother to make the parent if the current entry is the cwd,\n    // we've already checked it.\n    if (entry.absolute !== this.cwd) {\n      const parent = normalizeWindowsPath(\n        path.dirname(String(entry.absolute)),\n      )\n      if (parent !== this.cwd) {\n        const mkParent = this[MKDIR](parent, this.dmode)\n        if (mkParent) {\n          return this[ONERROR](mkParent as Error, entry)\n        }\n      }\n    }\n\n    const [lstatEr, st] = callSync(() =>\n      fs.lstatSync(String(entry.absolute)),\n    )\n    if (\n      st &&\n      (this.keep ||\n        /* c8 ignore next */\n        (this.newer && st.mtime > (entry.mtime ?? st.mtime)))\n    ) {\n      return this[SKIP](entry)\n    }\n\n    if (lstatEr || this[ISREUSABLE](entry, st)) {\n      return this[MAKEFS](null, entry)\n    }\n\n    if (st.isDirectory()) {\n      if (entry.type === 'Directory') {\n        const needChmod =\n          this.chmod &&\n          entry.mode &&\n          (st.mode & 0o7777) !== entry.mode\n        const [er] =\n          needChmod ?\n            callSync(() => {\n              fs.chmodSync(String(entry.absolute), Number(entry.mode))\n            })\n          : []\n        return this[MAKEFS](er, entry)\n      }\n      // not a dir entry, have to remove it\n      const [er] = callSync(() =>\n        fs.rmdirSync(String(entry.absolute)),\n      )\n      this[MAKEFS](er, entry)\n    }\n\n    // not a dir, and not reusable.\n    // don't remove if it's the cwd, since we want that error.\n    const [er] =\n      entry.absolute === this.cwd ?\n        []\n      : callSync(() => unlinkFileSync(String(entry.absolute)))\n    this[MAKEFS](er, entry)\n  }\n\n  [FILE](entry: ReadEntry, done: () => void) {\n    const mode =\n      typeof entry.mode === 'number' ?\n        entry.mode & 0o7777\n      : this.fmode\n\n    const oner = (er?: null | Error | undefined) => {\n      let closeError\n      try {\n        fs.closeSync(fd)\n      } catch (e) {\n        closeError = e\n      }\n      if (er || closeError) {\n        this[ONERROR]((er as Error) || closeError, entry)\n      }\n      done()\n    }\n\n    let fd: number\n    try {\n      fd = fs.openSync(\n        String(entry.absolute),\n        getWriteFlag(entry.size),\n        mode,\n      )\n    } catch (er) {\n      return oner(er as Error)\n    }\n    const tx = this.transform ? this.transform(entry) || entry : entry\n    if (tx !== entry) {\n      tx.on('error', (er: Error) => this[ONERROR](er, entry))\n      entry.pipe(tx)\n    }\n\n    tx.on('data', (chunk: Buffer) => {\n      try {\n        fs.writeSync(fd, chunk, 0, chunk.length)\n      } catch (er) {\n        oner(er as Error)\n      }\n    })\n\n    tx.on('end', () => {\n      let er = null\n      // try both, falling futimes back to utimes\n      // if either fails, handle the first error\n      if (entry.mtime && !this.noMtime) {\n        const atime = entry.atime || new Date()\n        const mtime = entry.mtime\n        try {\n          fs.futimesSync(fd, atime, mtime)\n        } catch (futimeser) {\n          try {\n            fs.utimesSync(String(entry.absolute), atime, mtime)\n          } catch (utimeser) {\n            er = futimeser\n          }\n        }\n      }\n\n      if (this[DOCHOWN](entry)) {\n        const uid = this[UID](entry)\n        const gid = this[GID](entry)\n\n        try {\n          fs.fchownSync(fd, Number(uid), Number(gid))\n        } catch (fchowner) {\n          try {\n            fs.chownSync(\n              String(entry.absolute),\n              Number(uid),\n              Number(gid),\n            )\n          } catch (chowner) {\n            er = er || fchowner\n          }\n        }\n      }\n\n      oner(er as Error)\n    })\n  }\n\n  [DIRECTORY](entry: ReadEntry, done: () => void) {\n    const mode =\n      typeof entry.mode === 'number' ?\n        entry.mode & 0o7777\n      : this.dmode\n    const er = this[MKDIR](String(entry.absolute), mode)\n    if (er) {\n      this[ONERROR](er as Error, entry)\n      done()\n      return\n    }\n    if (entry.mtime && !this.noMtime) {\n      try {\n        fs.utimesSync(\n          String(entry.absolute),\n          entry.atime || new Date(),\n          entry.mtime,\n        )\n        /* c8 ignore next */\n      } catch (er) {}\n    }\n    if (this[DOCHOWN](entry)) {\n      try {\n        fs.chownSync(\n          String(entry.absolute),\n          Number(this[UID](entry)),\n          Number(this[GID](entry)),\n        )\n      } catch (er) {}\n    }\n    done()\n    entry.resume()\n  }\n\n  [MKDIR](dir: string, mode: number) {\n    try {\n      return mkdirSync(normalizeWindowsPath(dir), {\n        uid: this.uid,\n        gid: this.gid,\n        processUid: this.processUid,\n        processGid: this.processGid,\n        umask: this.processUmask,\n        preserve: this.preservePaths,\n        unlink: this.unlink,\n        cache: this.dirCache,\n        cwd: this.cwd,\n        mode: mode,\n      })\n    } catch (er) {\n      return er\n    }\n  }\n\n  [LINK](\n    entry: ReadEntry,\n    linkpath: string,\n    link: 'link' | 'symlink',\n    done: () => void,\n  ) {\n    const ls: `${typeof link}Sync` = `${link}Sync`\n    try {\n      fs[ls](linkpath, String(entry.absolute))\n      done()\n      entry.resume()\n    } catch (er) {\n      return this[ONERROR](er as Error, entry)\n    }\n  }\n}\n"]}