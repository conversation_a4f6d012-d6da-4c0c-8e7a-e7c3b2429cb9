{"version": 3, "file": "RuleCreator.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAK3B,MAAM,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAEpE,MAAM,MAAM,mBAAmB,CAC7B,UAAU,SAAS,MAAM,EACzB,UAAU,GAAG,OAAO,EACpB,OAAO,SAAS,SAAS,OAAO,EAAE,GAAG,EAAE,IACrC;IACF,IAAI,EAAE,UAAU,GAAG,gBAAgB,CAAC;CACrC,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC;AAEhE,MAAM,WAAW,oBAAoB,CACnC,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM;IAEzB,MAAM,EAAE,CACN,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EACnD,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAClC,YAAY,CAAC;IAClB,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,YAAY,CAC3B,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,EACzB,IAAI,GAAG,OAAO,CACd,SAAQ,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC;IACjD,IAAI,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;CAC/C;AAED,MAAM,WAAW,mBAAmB,CAClC,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,EACzB,IAAI,GAAG,OAAO,CACd,SAAQ,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC;IACjD,IAAI,EAAE,mBAAmB,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACrD,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;;;GAKG;AACH,wBAAgB,WAAW,CAAC,UAAU,GAAG,OAAO,EAC9C,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,IAKtC,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,EACzB,yBAIC,QAAQ,CACT,mBAAmB,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CACrD,KAAG,UAAU,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAYhD;yBA1Be,WAAW;sBA0DzB,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,QAEnB,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,KAChD,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC;;AAIlC,OAAO,EAAE,KAAK,YAAY,EAAE,KAAK,UAAU,EAAE,MAAM,mBAAmB,CAAC"}