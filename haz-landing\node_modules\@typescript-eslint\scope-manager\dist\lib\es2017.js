"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2017 = void 0;
const es2016_1 = require("./es2016");
const es2017_arraybuffer_1 = require("./es2017.arraybuffer");
const es2017_date_1 = require("./es2017.date");
const es2017_intl_1 = require("./es2017.intl");
const es2017_object_1 = require("./es2017.object");
const es2017_sharedmemory_1 = require("./es2017.sharedmemory");
const es2017_string_1 = require("./es2017.string");
const es2017_typedarrays_1 = require("./es2017.typedarrays");
exports.es2017 = {
    libs: [
        es2016_1.es2016,
        es2017_arraybuffer_1.es2017_arraybuffer,
        es2017_date_1.es2017_date,
        es2017_intl_1.es2017_intl,
        es2017_object_1.es2017_object,
        es2017_sharedmemory_1.es2017_sharedmemory,
        es2017_string_1.es2017_string,
        es2017_typedarrays_1.es2017_typedarrays,
    ],
    variables: [],
};
